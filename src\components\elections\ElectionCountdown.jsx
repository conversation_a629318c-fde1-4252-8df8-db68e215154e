import { useState, useEffect } from 'react';
import { Clock, Calendar } from 'lucide-react';

const ElectionCountdown = ({ endTime, onElectionEnd }) => {
  const [timeRemaining, setTimeRemaining] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    totalSeconds: 0,
    isExpired: false
  });

  const calculateTimeRemaining = () => {
    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const difference = end - now;

    if (difference <= 0) {
      return {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        totalSeconds: 0,
        isExpired: true
      };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return {
      days,
      hours,
      minutes,
      seconds,
      totalSeconds: Math.floor(difference / 1000),
      isExpired: false
    };
  };

  useEffect(() => {
    const updateTimer = () => {
      const newTime = calculateTimeRemaining();
      setTimeRemaining(newTime);

      // Call onElectionEnd when election expires
      if (newTime.isExpired && !timeRemaining.isExpired && onElectionEnd) {
        onElectionEnd();
      }
    };

    // Update immediately
    updateTimer();

    // Set up interval to update every second
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [endTime, onElectionEnd]);

  const getProgressPercentage = () => {
    const totalDuration = 1 * 24 * 60 * 60; // 2 days in seconds
    const elapsed = totalDuration - timeRemaining.totalSeconds;
    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
  };

  if (timeRemaining.isExpired) {
    return (
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
        <div className="flex items-center space-x-2 text-red-400">
          <Clock className="w-5 h-5" />
          <span className="font-semibold">Election Ended</span>
        </div>
        <p className="text-gray-300 text-sm mt-1">
          This election has concluded. Results are being finalized.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
      <div className="flex items-center space-x-2 text-blue-400 mb-3">
        <Calendar className="w-5 h-5" />
        <span className="font-semibold">Time Remaining</span>
      </div>

      {/* Countdown Display */}
      <div className="grid grid-cols-4 gap-2 mb-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{timeRemaining.days}</div>
          <div className="text-xs text-gray-400">Days</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{timeRemaining.hours}</div>
          <div className="text-xs text-gray-400">Hours</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{timeRemaining.minutes}</div>
          <div className="text-xs text-gray-400">Minutes</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{timeRemaining.seconds}</div>
          <div className="text-xs text-gray-400">Seconds</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-700 rounded-full h-2">
        <div
          className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
          style={{ width: `${getProgressPercentage()}%` }}
        ></div>
      </div>
      <p className="text-xs text-gray-400 mt-1 text-center">
        {getProgressPercentage().toFixed(1)}% complete
      </p>
    </div>
  );
};

export default ElectionCountdown;
