import { useEffect, useRef, useCallback } from 'react';
import useElectionStore from '../store/useElectionStore';

/**
 * Custom hook for managing real-time election updates
 * @param {string} stateId - State ID to monitor elections for
 * @param {number} interval - Update interval in milliseconds (default: 30000)
 * @param {boolean} enabled - Whether updates are enabled (default: true)
 */
export const useElectionUpdates = (stateId, interval = 30000, enabled = true) => {
  const intervalRef = useRef(null);
  const { refreshActiveElection, activeElection } = useElectionStore();

  const startUpdates = useCallback(async () => {
    if (!stateId || !enabled) return;

    try {
      await refreshActiveElection(stateId);
    } catch (error) {
      console.error('Failed to refresh election data:', error);
    }
  }, [stateId, enabled, refreshActiveElection]);

  const stopUpdates = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  useEffect(() => {
    if (!stateId || !enabled) {
      stopUpdates();
      return;
    }

    // Start immediate update
    startUpdates();

    // Set up interval for periodic updates
    intervalRef.current = setInterval(startUpdates, interval);

    // Cleanup on unmount or dependency change
    return () => {
      stopUpdates();
    };
  }, [stateId, interval, enabled, startUpdates, stopUpdates]);

  // Stop updates when component unmounts
  useEffect(() => {
    return () => {
      stopUpdates();
    };
  }, [stopUpdates]);

  return {
    isUpdating: false, // Could be enhanced to track update state
    lastUpdate: activeElection?.updatedAt,
    forceUpdate: startUpdates,
    stopUpdates
  };
};

/**
 * Hook for managing election countdown with automatic refresh when election ends
 * @param {string|Date} endTime - Election end time
 * @param {Function} onElectionEnd - Callback when election ends
 * @param {number} updateInterval - Countdown update interval in ms (default: 1000)
 */
export const useElectionCountdown = (endTime, onElectionEnd, updateInterval = 1000) => {
  const intervalRef = useRef(null);
  const hasEndedRef = useRef(false);

  const calculateTimeRemaining = useCallback(() => {
    if (!endTime) return { isExpired: true, totalSeconds: 0 };

    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const difference = end - now;

    if (difference <= 0) {
      return {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        totalSeconds: 0,
        isExpired: true
      };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return {
      days,
      hours,
      minutes,
      seconds,
      totalSeconds: Math.floor(difference / 1000),
      isExpired: false
    };
  }, [endTime]);

  useEffect(() => {
    if (!endTime) return;

    const updateCountdown = () => {
      const timeRemaining = calculateTimeRemaining();
      
      // Check if election just ended
      if (timeRemaining.isExpired && !hasEndedRef.current) {
        hasEndedRef.current = true;
        if (onElectionEnd) {
          onElectionEnd();
        }
      }
    };

    // Start countdown
    intervalRef.current = setInterval(updateCountdown, updateInterval);

    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [endTime, onElectionEnd, updateInterval, calculateTimeRemaining]);

  // Reset hasEnded flag when endTime changes
  useEffect(() => {
    hasEndedRef.current = false;
  }, [endTime]);

  return {
    timeRemaining: calculateTimeRemaining(),
    hasEnded: hasEndedRef.current
  };
};

/**
 * Hook for managing election data with automatic refresh and error handling
 * @param {string} stateId - State ID
 * @param {Object} options - Configuration options
 */
export const useElectionData = (stateId, options = {}) => {
  const {
    autoRefresh = true,
    refreshInterval = 30000,
    refreshOnFocus = true,
    refreshOnReconnect = true
  } = options;

  const { 
    activeElection, 
    electionHistory, 
    loading, 
    error,
    fetchActiveElection,
    fetchElectionHistory,
    clearElectionData
  } = useElectionStore();

  // Auto refresh functionality
  useElectionUpdates(stateId, refreshInterval, autoRefresh);

  // Refresh on window focus
  useEffect(() => {
    if (!refreshOnFocus) return;

    const handleFocus = () => {
      if (stateId) {
        fetchActiveElection(stateId, true);
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [stateId, refreshOnFocus, fetchActiveElection]);

  // Refresh on network reconnect
  useEffect(() => {
    if (!refreshOnReconnect) return;

    const handleOnline = () => {
      if (stateId) {
        fetchActiveElection(stateId, true);
      }
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [stateId, refreshOnReconnect, fetchActiveElection]);

  // Initial data fetch
  useEffect(() => {
    if (stateId) {
      fetchActiveElection(stateId);
      fetchElectionHistory(stateId, 1, 10);
    }

    // Cleanup on unmount
    return () => {
      clearElectionData();
    };
  }, [stateId, fetchActiveElection, fetchElectionHistory, clearElectionData]);

  return {
    activeElection,
    electionHistory,
    loading,
    error,
    refetch: () => stateId && fetchActiveElection(stateId, true),
    refetchHistory: (page = 1, limit = 10) => stateId && fetchElectionHistory(stateId, page, limit)
  };
};
