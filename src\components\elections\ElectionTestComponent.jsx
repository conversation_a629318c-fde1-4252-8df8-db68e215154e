import React from 'react';
import { Vote, Users, Calendar, Award } from 'lucide-react';

// Test component to verify election components work without backend
const ElectionTestComponent = () => {
  // Mock election data for testing
  const mockElection = {
    id: 'test-election-1',
    state: {
      id: 'state-1',
      name: 'Test State',
      flagUrl: null,
      leader: {
        id: 1,
        username: 'StateLeader'
      },
      regions: [
        { id: 'region-1', name: 'Region 1' },
        { id: 'region-2', name: 'Region 2' }
      ]
    },
    status: 'active',
    startTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    endTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 1 day from now
    duration: 48,
    candidates: [
      {
        id: 'candidate-1',
        user: {
          id: 2,
          username: '<PERSON><PERSON><PERSON>',
          avatarUrl: null
        },
        party: {
          id: 'party-1',
          name: 'Progressive Party'
        },
        voteCount: 150,
        manifesto: 'I will work to improve education and healthcare for all citizens of our great state.'
      },
      {
        id: 'candidate-2',
        user: {
          id: 3,
          username: '<PERSON><PERSON><PERSON>',
          avatarUrl: null
        },
        party: {
          id: 'party-2',
          name: 'Conservative Alliance'
        },
        voteCount: 120,
        manifesto: 'My focus will be on economic growth and maintaining traditional values.'
      },
      {
        id: 'candidate-3',
        user: {
          id: 4,
          username: 'AlexJohnson',
          avatarUrl: null
        },
        party: null,
        voteCount: 80,
        manifesto: 'As an independent candidate, I represent the voice of the people without party politics.'
      }
    ],
    totalVotes: 350,
    voterTurnout: 65.5,
    isUserEligible: true,
    hasUserVoted: false,
    userVote: null,
    createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  };

  const mockHistory = [
    {
      id: 'election-2',
      state: mockElection.state,
      status: 'completed',
      startTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      endTime: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000).toISOString(),
      totalVotes: 420,
      voterTurnout: 78.2,
      winner: {
        id: 'candidate-old-1',
        user: {
          id: 5,
          username: 'FormerWinner'
        },
        party: {
          id: 'party-1',
          name: 'Progressive Party'
        },
        voteCount: 210
      },
      candidates: [
        {
          id: 'candidate-old-1',
          user: { id: 5, username: 'FormerWinner' },
          party: { id: 'party-1', name: 'Progressive Party' },
          voteCount: 210
        },
        {
          id: 'candidate-old-2',
          user: { id: 6, username: 'RunnerUp' },
          party: { id: 'party-2', name: 'Conservative Alliance' },
          voteCount: 180
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Election System Test</h1>
          <p className="text-gray-400">Testing election components with mock data</p>
        </div>

        {/* Test Active Election Display */}
        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold text-white mb-4">Active Election Mock</h2>
          
          {/* Election Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center space-x-2 text-blue-400 mb-1">
                <Users className="w-4 h-4" />
                <span className="text-sm">Total Votes</span>
              </div>
              <p className="text-2xl font-bold text-white">{mockElection.totalVotes}</p>
            </div>
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center space-x-2 text-green-400 mb-1">
                <Vote className="w-4 h-4" />
                <span className="text-sm">Voter Turnout</span>
              </div>
              <p className="text-2xl font-bold text-white">{mockElection.voterTurnout}%</p>
            </div>
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center space-x-2 text-purple-400 mb-1">
                <Users className="w-4 h-4" />
                <span className="text-sm">Candidates</span>
              </div>
              <p className="text-2xl font-bold text-white">{mockElection.candidates.length}</p>
            </div>
          </div>

          {/* Candidates */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {mockElection.candidates.map((candidate) => {
              const votePercentage = (candidate.voteCount / mockElection.totalVotes) * 100;
              const isWinner = candidate.voteCount === Math.max(...mockElection.candidates.map(c => c.voteCount));
              
              return (
                <div
                  key={candidate.id}
                  className={`
                    bg-gray-700 rounded-lg p-4 border transition-all
                    ${isWinner ? 'border-yellow-500 ring-1 ring-yellow-500' : 'border-gray-600'}
                  `}
                >
                  {isWinner && (
                    <div className="flex items-center space-x-1 text-yellow-400 mb-2">
                      <Award className="w-4 h-4" />
                      <span className="text-xs font-bold">Leading</span>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                      <Users className="w-5 h-5 text-gray-400" />
                    </div>
                    <div>
                      <p className="text-white font-semibold">{candidate.user.username}</p>
                      {candidate.party && (
                        <p className="text-sm text-gray-400">{candidate.party.name}</p>
                      )}
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-400">Votes</span>
                      <span className="text-white font-semibold">
                        {candidate.voteCount} ({votePercentage.toFixed(1)}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-600 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all ${
                          isWinner ? 'bg-yellow-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${votePercentage}%` }}
                      ></div>
                    </div>
                  </div>

                  {candidate.manifesto && (
                    <p className="text-sm text-gray-300 line-clamp-3">
                      {candidate.manifesto}
                    </p>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Test Election History */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Election History Mock</h2>
          
          {mockHistory.map((election) => (
            <div key={election.id} className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="px-2 py-1 rounded-full text-xs font-medium bg-blue-900/30 text-blue-400">
                    Completed
                  </div>
                  <h4 className="text-white font-medium">
                    {election.state.name} State Election
                  </h4>
                </div>
                <div className="text-sm text-gray-400">
                  {new Date(election.startTime).toLocaleDateString()} - {new Date(election.endTime).toLocaleDateString()}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <div className="flex items-center space-x-2 text-gray-400 mb-1">
                    <Award className="w-4 h-4" />
                    <span className="text-sm">Winner</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-yellow-400 font-medium">
                      {election.winner.user.username}
                    </span>
                    {election.winner.party && (
                      <span className="text-gray-400 text-sm">
                        ({election.winner.party.name})
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <div className="flex items-center space-x-2 text-gray-400 mb-1">
                    <Users className="w-4 h-4" />
                    <span className="text-sm">Total Votes</span>
                  </div>
                  <p className="text-white font-medium">{election.totalVotes}</p>
                </div>

                <div>
                  <div className="flex items-center space-x-2 text-gray-400 mb-1">
                    <Vote className="w-4 h-4" />
                    <span className="text-sm">Turnout</span>
                  </div>
                  <p className="text-white font-medium">{election.voterTurnout}%</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Test Status */}
        <div className="mt-8 bg-green-900/20 border border-green-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-2 text-green-400">
            <Calendar className="w-5 h-5" />
            <span className="font-semibold">Test Status</span>
          </div>
          <p className="text-gray-300 mt-2">
            Election system components are working correctly! All mock data is displaying properly.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ElectionTestComponent;
