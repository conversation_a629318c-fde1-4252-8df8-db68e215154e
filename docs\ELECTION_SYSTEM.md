# State Election Voting System

## Overview

The State Election Voting System is a comprehensive React-based frontend implementation that integrates with the NestJS backend to provide democratic election functionality for states in the Warfront Nations game.

## Features

### Core Functionality
- **Real-time Election Dashboard**: View active elections with live vote counts
- **Voting Interface**: Secure voting system with confirmation modals
- **Election History**: Paginated view of past elections with search and filtering
- **Countdown Timer**: Real-time countdown showing time remaining in elections
- **Candidate Management**: Display candidate information, manifestos, and vote counts
- **Responsive Design**: Mobile-friendly interface with adaptive layouts

### User Experience
- **Eligibility Checking**: Automatic verification of voting eligibility
- **Vote Confirmation**: Multi-step voting process with confirmation
- **Real-time Updates**: Automatic refresh of election data every 30 seconds
- **Progress Tracking**: Visual progress bars and election timeline
- **Error Handling**: Comprehensive error handling with user-friendly messages

## Architecture

### Components Structure
```
src/components/elections/
├── ElectionDashboard.jsx      # Main dashboard component
├── ActiveElection.jsx         # Active election display with tabs
├── VotingInterface.jsx        # Voting UI with candidate selection
├── ElectionHistory.jsx        # Historical elections with pagination
├── ElectionCountdown.jsx      # Real-time countdown timer
└── CandidateCard.jsx         # Individual candidate display
```

### Pages
```
src/pages/
└── ElectionsPage.jsx         # Main elections page with navbar
```

### State Management
```
src/store/
└── useElectionStore.js       # Zustand store for election data
```

### Services
```
src/services/api/
└── stateElection.service.ts  # API integration service
```

### Types
```
src/types/
└── stateElection.ts          # TypeScript interfaces and enums
```

### Utilities
```
src/utils/
└── electionUtils.js          # Helper functions for elections
```

### Hooks
```
src/hooks/
└── useElectionUpdates.js     # Custom hooks for real-time updates
```

## API Integration

### Endpoints Used
- `GET /state-elections/state/{stateId}/active` - Get active election
- `GET /state-elections/state/{stateId}/history` - Get election history
- `POST /state-elections/{electionId}/vote` - Submit vote
- `GET /state-elections/{electionId}` - Get specific election
- `GET /state-elections/statistics` - Get election statistics

### Data Flow
1. User navigates to `/elections`
2. System determines user's state affiliation
3. Fetches active election and history for user's state
4. Displays election dashboard with real-time updates
5. User can vote if eligible and election is active
6. Real-time updates show current vote counts and status

## Key Features Implementation

### Real-time Updates
- Automatic polling every 30 seconds
- Manual refresh functionality
- Update on window focus and network reconnection
- WebSocket support ready for future implementation

### Voting Process
1. **Eligibility Check**: Verify user can vote
2. **Candidate Selection**: Choose from available candidates
3. **Confirmation Modal**: Confirm vote choice
4. **Submission**: Submit vote to backend
5. **Success Feedback**: Show confirmation and update UI
6. **State Update**: Refresh election data

### Security Features
- JWT token authentication for all API calls
- Prevention of multiple votes from same user
- Input validation for vote submission
- Rate limiting protection

### Responsive Design
- Mobile-first approach
- Adaptive grid layouts
- Touch-friendly interfaces
- Optimized for various screen sizes

## Usage

### Navigation
Users can access the election system through:
- Navbar link: "Elections"
- Direct URL: `/elections`

### User States
1. **No State Affiliation**: Shows message to join a state
2. **State Member**: Can view and participate in state elections
3. **State Leader**: Full access to election management

### Election States
1. **No Active Election**: Shows placeholder with history access
2. **Active Election**: Full voting interface with real-time updates
3. **Election Ended**: Shows results and prevents further voting

## Performance Optimizations

### Caching Strategy
- 30-second cache for election data
- Conditional API calls based on cache freshness
- Optimistic updates for better UX

### Code Splitting
- Lazy loading for election history
- Component-level optimization with React.memo
- Efficient re-rendering with useMemo and useCallback

### Network Optimization
- Debounced API calls
- Request deduplication
- Automatic retry on network errors

## Error Handling

### User-Friendly Messages
- Network connectivity issues
- Authentication failures
- Voting eligibility problems
- Server errors

### Fallback Behavior
- Cached data display during network issues
- Graceful degradation of real-time features
- Retry mechanisms for failed requests

## Accessibility

### WCAG Compliance
- Screen reader support
- Keyboard navigation
- High contrast mode support
- Focus management for modals

### Interactive Elements
- ARIA labels for all controls
- Semantic HTML structure
- Clear visual feedback
- Accessible color schemes

## Testing Strategy

### Component Testing
- Unit tests for all components
- Mock API responses
- User interaction testing
- Error state testing

### Integration Testing
- Complete voting flow testing
- Real-time update testing
- State management testing
- API integration testing

### E2E Testing
- Full user journey testing
- Cross-browser compatibility
- Mobile device testing
- Performance testing

## Future Enhancements

### Planned Features
- WebSocket integration for instant updates
- Election result visualization (charts/graphs)
- Candidate profile pages
- Election notifications/reminders
- Social sharing of results
- Export functionality

### Technical Improvements
- Progressive Web App (PWA) support
- Offline functionality
- Push notifications
- Advanced caching strategies

## Configuration

### Environment Variables
```env
VITE_API_BASE_URL=http://localhost:3000
```

### Customization Options
- Update intervals for real-time data
- Pagination limits for history
- UI theme and styling
- Error message customization

## Deployment

### Build Process
```bash
npm run build
```

### Dependencies
- React 19.0.0
- React Router DOM 7.3.0
- Zustand 5.0.3
- Lucide React 0.511.0
- Axios 1.8.4

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Progressive enhancement for older browsers
