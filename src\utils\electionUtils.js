/**
 * Calculate time remaining until election ends
 * @param {string|Date} endTime - Election end time
 * @returns {Object} Time remaining object
 */
export const calculateTimeRemaining = (endTime) => {
  const now = new Date().getTime();
  const end = new Date(endTime).getTime();
  const difference = end - now;

  if (difference <= 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      totalSeconds: 0,
      isExpired: true
    };
  }

  const days = Math.floor(difference / (1000 * 60 * 60 * 24));
  const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((difference % (1000 * 60)) / 1000);

  return {
    days,
    hours,
    minutes,
    seconds,
    totalSeconds: Math.floor(difference / 1000),
    isExpired: false
  };
};

/**
 * Format election date for display
 * @param {string|Date} dateString - Date to format
 * @returns {string} Formatted date string
 */
export const formatElectionDate = (dateString) => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Format election date range
 * @param {string|Date} startDate - Election start date
 * @param {string|Date} endDate - Election end date
 * @returns {string} Formatted date range
 */
export const formatElectionDateRange = (startDate, endDate) => {
  const start = new Date(startDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
  const end = new Date(endDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
  return `${start} - ${end}`;
};

/**
 * Calculate vote percentage for a candidate
 * @param {number} candidateVotes - Votes for the candidate
 * @param {number} totalVotes - Total votes in election
 * @returns {number} Percentage (0-100)
 */
export const calculateVotePercentage = (candidateVotes, totalVotes) => {
  if (totalVotes === 0) return 0;
  return (candidateVotes / totalVotes) * 100;
};

/**
 * Determine if a candidate is the winner
 * @param {Object} candidate - Candidate object
 * @param {Array} allCandidates - All candidates in the election
 * @param {number} totalVotes - Total votes cast
 * @returns {boolean} True if candidate is winning
 */
export const isWinningCandidate = (candidate, allCandidates, totalVotes) => {
  if (totalVotes === 0) return false;

  // Find the candidate with the most votes
  const maxVotes = Math.max(...allCandidates.map(c => c.voteCount));
  return candidate.voteCount === maxVotes && candidate.voteCount > 0;
};

/**
 * Sort candidates by vote count (descending)
 * @param {Array} candidates - Array of candidate objects
 * @returns {Array} Sorted candidates
 */
export const sortCandidatesByVotes = (candidates) => {
  return [...candidates].sort((a, b) => b.voteCount - a.voteCount);
};

/**
 * Get election status color class
 * @param {string} status - Election status
 * @returns {string} CSS class for status color
 */
export const getElectionStatusColor = (status) => {
  switch (status) {
    case 'active':
      return 'bg-green-900/30 text-green-400';
    case 'completed':
      return 'bg-blue-900/30 text-blue-400';
    case 'pending':
      return 'bg-yellow-900/30 text-yellow-400';
    case 'cancelled':
      return 'bg-red-900/30 text-red-400';
    default:
      return 'bg-gray-600 text-gray-300';
  }
};

/**
 * Check if user can vote in election
 * @param {Object} election - Election object
 * @param {Object} user - User object
 * @returns {boolean} True if user can vote
 */
export const canUserVote = (election, user) => {
  if (!election || !user) return false;
  if (election.status !== 'active') return false;
  if (election.hasUserVoted) return false;
  if (!election.isUserEligible) return false;

  // Check if election has ended
  const timeRemaining = calculateTimeRemaining(election.endTime);
  if (timeRemaining.isExpired) return false;

  return true;
};

/**
 * Get progress percentage for election timeline
 * @param {string|Date} startTime - Election start time
 * @param {string|Date} endTime - Election end time
 * @returns {number} Progress percentage (0-100)
 */
export const getElectionProgress = (startTime, endTime) => {
  const now = new Date().getTime();
  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();

  if (now < start) return 0;
  if (now > end) return 100;

  const totalDuration = end - start;
  const elapsed = now - start;

  return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
};

/**
 * Normalize election data from backend to match frontend expectations
 * @param {Object} election - Raw election data from backend
 * @returns {Object} Normalized election data
 */
export const normalizeElectionData = (election) => {
  if (!election) return null;

  // First normalize candidates
  const normalizedCandidates = (election.candidates || []).map(candidate => {
    // Handle new backend structure: { userId, username, votes }
    if (candidate.userId && candidate.username) {
      return {
        id: candidate.userId,
        userId: candidate.userId,
        username: candidate.username,
        voteCount: candidate.votes || 0,
        votes: candidate.votes || 0,
        user: {
          id: candidate.userId,
          username: candidate.username,
          avatarUrl: candidate.avatarUrl || null
        },
        party: candidate.party || null,
        manifesto: candidate.manifesto || ''
      };
    }

    // Handle old backend structure: { id, user: { id, username }, voteCount }
    return {
      ...candidate,
      voteCount: candidate.voteCount || candidate.votes || 0,
      votes: candidate.votes || candidate.voteCount || 0,
      user: candidate.user || {
        id: candidate.id || candidate.userId || 0,
        username: candidate.username || 'Unknown'
      },
      party: candidate.party || null,
      manifesto: candidate.manifesto || ''
    };
  });

  // Calculate total votes from candidates if not provided
  const calculatedTotalVotes = calculateTotalVotes(normalizedCandidates);

  return {
    ...election,
    // Normalize date fields
    startTime: election.startedAt || election.startTime,
    endTime: election.endedAt || election.endTime,

    // Ensure arrays exist
    candidates: normalizedCandidates,
    votes: election.votes || [],
    voters: election.voters || [],

    // Ensure numeric fields exist - use calculated total if backend doesn't provide it
    totalVotes: election.totalVotes || calculatedTotalVotes,
    voterTurnout: election.voterTurnout || 0,
    duration: election.duration || 48,

    // Ensure boolean fields exist
    isUserEligible: election.isUserEligible !== undefined ? election.isUserEligible : true,
    hasUserVoted: election.hasUserVoted || false,

    // Normalize state data
    state: election.state ? {
      ...election.state,
      name: election.state.name || 'Unknown State'
    } : null
  };
};

/**
 * Calculate total votes from candidates
 * @param {Array} candidates - Array of candidate objects
 * @returns {number} Total vote count
 */
export const calculateTotalVotes = (candidates) => {
  if (!Array.isArray(candidates)) return 0;
  return candidates.reduce((total, candidate) => {
    const votes = candidate.voteCount || candidate.votes || 0;
    return total + votes;
  }, 0);
};

/**
 * Calculate voter turnout percentage
 * @param {number} totalVotes - Total votes cast
 * @param {number} eligibleVoters - Total eligible voters
 * @returns {number} Turnout percentage
 */
export const calculateVoterTurnout = (totalVotes, eligibleVoters) => {
  if (!eligibleVoters || eligibleVoters === 0) return 0;
  return (totalVotes / eligibleVoters) * 100;
};
