import React, { useState, useEffect } from 'react';
import { Calendar, Users, Award, ChevronLeft, ChevronRight, Search, Filter } from 'lucide-react';
import { Link } from 'react-router-dom';
import useElectionStore from '../../store/useElectionStore';

const ElectionHistory = ({ stateId }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const {
    electionHistory,
    historyPage,
    historyLimit,
    historyTotal,
    historyTotalPages,
    fetchElectionHistory,
    loading
  } = useElectionStore();

  useEffect(() => {
    fetchElectionHistory(stateId, 1, 10);
  }, [stateId, fetchElectionHistory]);

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= historyTotalPages) {
      fetchElectionHistory(stateId, newPage, historyLimit);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateRange = (startDate, endDate) => {
    const start = formatDate(startDate);
    const end = formatDate(endDate);
    return `${start} - ${end}`;
  };

  const filteredElections = electionHistory.filter(election => {
    const matchesSearch = election.state.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (election.winner?.user.username || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || election.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  if (loading && electionHistory.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-700 rounded w-1/4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-white">Election History</h3>
        <div className="text-sm text-gray-400">
          {historyTotal} total elections
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search elections..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="pl-10 pr-8 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Elections</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Elections List */}
      {filteredElections.length === 0 ? (
        <div className="text-center py-8">
          <Calendar className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">No elections found</p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredElections.map((election) => (
            <div
              key={election.id}
              className="bg-gray-700 rounded-lg p-4 hover:bg-gray-600 transition-colors"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className={`
                    px-2 py-1 rounded-full text-xs font-medium
                    ${election.status === 'completed'
                      ? 'bg-green-900/30 text-green-400'
                      : 'bg-gray-600 text-gray-300'
                    }
                  `}>
                    {election.status.charAt(0).toUpperCase() + election.status.slice(1)}
                  </div>
                  <h4 className="text-white font-medium">
                    {election.state.name} State Election
                  </h4>
                </div>
                <div className="text-sm text-gray-400">
                  {formatDateRange(election.startTime, election.endTime)}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Winner */}
                <div>
                  <div className="flex items-center space-x-2 text-gray-400 mb-1">
                    <Award className="w-4 h-4" />
                    <span className="text-sm">Winner</span>
                  </div>
                  {election.winner ? (
                    <div className="flex items-center space-x-2">
                      <Link
                        to={`/users/${election.winner.user.id}`}
                        className="text-yellow-400 hover:text-yellow-300 font-medium transition-colors"
                      >
                        {election.winner.user.username}
                      </Link>
                      {election.winner.party && (
                        <span className="text-gray-400 text-sm">
                          ({election.winner.party.name})
                        </span>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-500">No winner declared</span>
                  )}
                </div>

                {/* Total Votes */}
                <div>
                  <div className="flex items-center space-x-2 text-gray-400 mb-1">
                    <Users className="w-4 h-4" />
                    <span className="text-sm">Total Votes</span>
                  </div>
                  <p className="text-white font-medium">{election.totalVotes}</p>
                </div>

                {/* Voter Turnout */}
                <div>
                  <div className="flex items-center space-x-2 text-gray-400 mb-1">
                    <Users className="w-4 h-4" />
                    <span className="text-sm">Turnout</span>
                  </div>
                  <p className="text-white font-medium">{(election.voterTurnout || 0).toFixed(1)}%</p>
                </div>
              </div>

              {/* Candidates Summary */}
              {election.candidates.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-600">
                  <p className="text-sm text-gray-400 mb-2">
                    {election.candidates.length} candidates participated
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {election.candidates.slice(0, 5).map((candidate) => (
                      <Link
                        key={candidate.id}
                        to={`/users/${candidate.user.id}`}
                        className="text-xs bg-gray-600 hover:bg-gray-500 text-gray-300 px-2 py-1 rounded transition-colors"
                      >
                        {candidate.user.username}
                      </Link>
                    ))}
                    {election.candidates.length > 5 && (
                      <span className="text-xs text-gray-400 px-2 py-1">
                        +{election.candidates.length - 5} more
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {historyTotalPages > 1 && (
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-700">
          <div className="text-sm text-gray-400">
            Page {historyPage} of {historyTotalPages}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handlePageChange(historyPage - 1)}
              disabled={historyPage === 1}
              className="p-2 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
            >
              <ChevronLeft className="w-4 h-4 text-gray-400" />
            </button>
            <span className="px-3 py-1 bg-gray-700 rounded text-white text-sm">
              {historyPage}
            </span>
            <button
              onClick={() => handlePageChange(historyPage + 1)}
              disabled={historyPage === historyTotalPages}
              className="p-2 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
            >
              <ChevronRight className="w-4 h-4 text-gray-400" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ElectionHistory;
