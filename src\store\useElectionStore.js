import { create } from "zustand";
import { stateElectionService } from "../services/api/stateElection.service";
import { showErrorToast } from "../utils/showErrorToast";

const useElectionStore = create((set, get) => ({
  // State
  activeElection: null,
  electionHistory: [],
  loading: false,
  error: null,
  lastUpdated: null,
  
  // Pagination for history
  historyPage: 1,
  historyLimit: 10,
  historyTotal: 0,
  historyTotalPages: 0,

  // Actions
  fetchActiveElection: async (stateId, force = false) => {
    const state = get();
    
    // Return cached data if recent and not forcing
    if (state.activeElection && !force && state.lastUpdated) {
      const timeDiff = Date.now() - state.lastUpdated;
      if (timeDiff < 30000) { // 30 seconds cache
        return state.activeElection;
      }
    }

    set({ loading: true, error: null });
    
    try {
      const election = await stateElectionService.getActiveElection(stateId);
      set({
        activeElection: election,
        loading: false,
        lastUpdated: Date.now()
      });
      return election;
    } catch (error) {
      console.error('Failed to fetch active election:', error);
      set({
        error: 'Failed to fetch active election',
        loading: false
      });
      showErrorToast('Failed to load election data');
      return null;
    }
  },

  fetchElectionHistory: async (stateId, page = 1, limit = 10) => {
    set({ loading: true, error: null });
    
    try {
      const history = await stateElectionService.getElectionHistory(stateId, page, limit);
      set({
        electionHistory: history.elections,
        historyPage: history.page,
        historyLimit: history.limit,
        historyTotal: history.total,
        historyTotalPages: history.totalPages,
        loading: false
      });
      return history;
    } catch (error) {
      console.error('Failed to fetch election history:', error);
      set({
        error: 'Failed to fetch election history',
        loading: false
      });
      showErrorToast('Failed to load election history');
      return null;
    }
  },

  submitVote: async (electionId, candidateId) => {
    set({ loading: true, error: null });
    
    try {
      const updatedElection = await stateElectionService.submitVote(electionId, { candidateId });
      set({
        activeElection: updatedElection,
        loading: false,
        lastUpdated: Date.now()
      });
      return updatedElection;
    } catch (error) {
      console.error('Failed to submit vote:', error);
      set({
        error: 'Failed to submit vote',
        loading: false
      });
      throw error; // Re-throw to handle in component
    }
  },

  refreshActiveElection: async (stateId) => {
    return get().fetchActiveElection(stateId, true);
  },

  clearElectionData: () => {
    set({
      activeElection: null,
      electionHistory: [],
      error: null,
      lastUpdated: null,
      historyPage: 1,
      historyTotal: 0,
      historyTotalPages: 0
    });
  },

  updateElectionInRealTime: (updatedElection) => {
    set({
      activeElection: updatedElection,
      lastUpdated: Date.now()
    });
  }
}));

export default useElectionStore;
